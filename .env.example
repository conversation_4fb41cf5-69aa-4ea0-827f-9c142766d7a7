# AutoGen编程工作流环境配置示例
# 复制此文件为 .env 并填入实际值

# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1  # 第三方平台API地址
# 常见第三方平台示例：
# OPENAI_BASE_URL=https://api.deepseek.com/v1      # DeepSeek
# OPENAI_BASE_URL=https://api.moonshot.cn/v1       # Moonshot
# OPENAI_BASE_URL=https://api.zhipuai.cn/v4        # 智谱AI
# OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1  # 阿里云
OPENAI_ORG_ID=your_organization_id_here  # 可选

# AutoGen工作流配置
AUTOGEN_MODEL=gpt-4o                    # 默认模型
AUTOGEN_MAX_MESSAGES=20                 # 最大消息数
AUTOGEN_TEMPERATURE=0.7                 # 模型温度
AUTOGEN_LOGGING=true                    # 启用日志
AUTOGEN_OUTPUT_DIR=output               # 输出目录

# 代理配置（如果需要）
HTTP_PROXY=http://proxy.example.com:8080
HTTPS_PROXY=http://proxy.example.com:8080

# 调试配置
DEBUG=false
VERBOSE=false
