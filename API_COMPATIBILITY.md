# API兼容性说明

## 问题描述

在使用第三方OpenAI兼容API时，可能会遇到以下错误：

```
AttributeError: 'str' object has no attribute 'usage'
```

## 原因分析

这个错误是因为第三方API的响应格式与OpenAI官方API不完全一致导致的：

1. **OpenAI官方API响应格式**：
   ```json
   {
     "choices": [...],
     "usage": {
       "prompt_tokens": 10,
       "completion_tokens": 20,
       "total_tokens": 30
     }
   }
   ```

2. **部分第三方API响应格式**：
   - 可能直接返回字符串
   - 或者缺少`usage`字段
   - 或者`usage`字段格式不同

## 解决方案

### 方案1：使用OpenAI官方API

最简单的解决方案是使用OpenAI官方API：

```bash
# 在.env文件中设置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1  # 或者注释掉这行
```

然后运行：
```bash
python demo_with_openai.py
```

### 方案2：寻找兼容的第三方API

一些第三方API提供更好的OpenAI兼容性：

1. **DeepSeek API**：
   ```
   OPENAI_BASE_URL=https://api.deepseek.com/v1
   ```

2. **Moonshot API**：
   ```
   OPENAI_BASE_URL=https://api.moonshot.cn/v1
   ```

3. **智谱AI**：
   ```
   OPENAI_BASE_URL=https://open.bigmodel.cn/api/paas/v4
   ```

### 方案3：自定义模型客户端

如果需要使用特定的第三方API，可以创建自定义的模型客户端来处理响应格式差异。

## 测试API兼容性

使用以下脚本测试API兼容性：

```bash
python simple_test.py
```

如果看到以下输出，说明API兼容：
```
✅ 成功创建模型客户端
✅ 成功关闭客户端
```

## 推荐配置

### 开发和测试环境

```env
# 使用OpenAI官方API（最稳定）
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
AUTOGEN_MODEL=gpt-3.5-turbo
```

### 生产环境

```env
# 使用兼容性好的第三方API
OPENAI_API_KEY=your_api_key
OPENAI_BASE_URL=https://api.deepseek.com/v1
AUTOGEN_MODEL=deepseek-chat
```

## 当前状态

- ✅ **OpenAI官方API**: 完全兼容
- ✅ **DeepSeek API**: 兼容性良好
- ✅ **Moonshot API**: 兼容性良好
- ⚠️  **其他第三方API**: 需要测试验证
- ❌ **dmxapi.cn**: 响应格式不兼容

## 下一步计划

1. 创建API适配器来处理不同的响应格式
2. 添加更多第三方API的兼容性测试
3. 提供API响应格式转换工具

## 使用建议

1. **优先使用OpenAI官方API**进行开发和测试
2. **选择兼容性好的第三方API**用于生产环境
3. **在切换API时先进行兼容性测试**
4. **保留OpenAI官方API作为备选方案**
