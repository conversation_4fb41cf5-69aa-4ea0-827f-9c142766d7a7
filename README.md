# AutoGen编程工作流

基于Microsoft AutoGen框架构建的智能编程工作流，实现了三个专业AI Agent的协作：代码编写、代码审查和代码优化。

## 🌟 特性

- **智能协作**: 三个专业Agent协同工作，确保代码质量
- **自动化流程**: 从需求分析到代码优化的完整自动化流程
- **灵活配置**: 支持自定义Agent行为和工作流程
- **最新技术**: 基于AutoGen最新版本和最佳实践

## 🏗️ 架构设计

### Agent角色

1. **CodeWriter (代码编写者)**
   - 分析用户需求
   - 编写高质量代码
   - 遵循编程最佳实践
   - 添加文档和注释

2. **CodeReviewer (代码审查者)**
   - 检查代码质量
   - 识别潜在问题
   - 提出改进建议
   - 评估代码安全性

3. **CodeOptimizer (代码优化者)**
   - 根据审查建议优化代码
   - 提高性能和可读性
   - 重构代码结构
   - 确保功能完整性

### 工作流程

```mermaid
graph TD
    A[用户输入需求] --> B[CodeWriter编写代码]
    B --> C[CodeReviewer审查代码]
    C --> D{审查通过?}
    D -->|是| E[流程结束]
    D -->|否| F[CodeOptimizer优化代码]
    F --> C
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 设置API密钥

```bash
# 设置OpenAI API密钥
export OPENAI_API_KEY="your-api-key-here"
```

或者创建 `.env` 文件：
```bash
cp .env.example .env
# 编辑.env文件，填入你的API密钥
```

### 3. 运行示例

#### 🎯 最简单的方式 - 快速启动脚本
```bash
# 交互模式
python quick_start.py

# 演示模式
python quick_start.py --demo

# 运行测试
python quick_start.py --test
```

#### 📚 使用示例脚本
```bash
# 快速演示
python workflow_examples.py demo

# 交互模式
python workflow_examples.py
```

#### 🔧 直接使用工作流
```python
import asyncio
from autogen_programming_workflow import ProgrammingWorkflow

async def main():
    workflow = ProgrammingWorkflow()

    task = """
    编写一个Python函数，计算两个数的最大公约数
    """

    await workflow.run_workflow(task)

asyncio.run(main())
```

#### 📁 批处理模式
```bash
# 从文件读取任务
echo "编写一个排序算法" > task.txt
python quick_start.py --batch task.txt --output results/
```

## 📚 使用示例

### 基本用法

```python
from autogen_programming_workflow import ProgrammingWorkflow

# 创建工作流实例
workflow = ProgrammingWorkflow(
    model_name="gpt-4o",  # 可选：指定模型
    api_key="your-key"    # 可选：直接提供API密钥
)

# 定义编程任务
task = """
请实现一个Python类Calculator，包含基本的数学运算功能：
1. 加法、减法、乘法、除法
2. 支持链式调用
3. 包含错误处理
4. 添加完整的文档
"""

# 运行工作流
await workflow.run_workflow(task)
```

### 高级配置

```python
# 自定义Agent系统消息
workflow = ProgrammingWorkflow()

# 可以通过修改agent的system_message来自定义行为
workflow.code_writer.system_message = "你是一个专注于性能优化的代码编写者..."
```

## 🎯 示例场景

项目包含多个预设示例：

1. **数据处理类** - 实现数据过滤、转换和聚合功能
2. **算法实现** - 二分查找算法的完整实现
3. **Web API设计** - FastAPI用户管理系统
4. **设计模式** - 观察者模式的Python实现

## ⚙️ 配置选项

### 模型配置
```python
workflow = ProgrammingWorkflow(
    model_name="gpt-4o",      # 模型名称
    api_key="your-key",       # API密钥
)
```

### 终止条件
工作流支持多种终止条件：
- 文本关键词终止（如"OPTIMIZATION_COMPLETE"）
- 最大消息数限制
- 自定义终止逻辑

### 选择器函数
自定义Agent选择逻辑：
```python
def custom_selector(messages):
    # 自定义选择逻辑
    return agent_name
```

## 🔧 开发指南

### 扩展Agent

```python
def create_custom_agent():
    return AssistantAgent(
        name="CustomAgent",
        model_client=model_client,
        system_message="自定义系统消息...",
        description="Agent描述"
    )
```

### 自定义工作流

```python
class CustomWorkflow(ProgrammingWorkflow):
    def _create_team(self):
        # 自定义团队配置
        pass
    
    def custom_selector_func(self, messages):
        # 自定义选择逻辑
        pass
```

## 📋 最佳实践

1. **明确任务描述**: 提供详细、具体的编程需求
2. **合理设置终止条件**: 避免无限循环
3. **监控Token使用**: 注意API调用成本
4. **保存重要结果**: 及时保存生成的代码

## 🐛 故障排除

### 常见问题

1. **API密钥错误**
   ```
   解决方案：检查OPENAI_API_KEY环境变量设置
   ```

2. **依赖包版本冲突**
   ```bash
   pip install --upgrade autogen-agentchat autogen-core autogen-ext
   ```

3. **网络连接问题**
   ```
   解决方案：检查网络连接和代理设置
   ```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请：
1. 查看文档和示例
2. 搜索已有Issue
3. 创建新Issue描述问题

---

**注意**: 使用前请确保已正确设置OpenAI API密钥，并了解相关的使用费用。
