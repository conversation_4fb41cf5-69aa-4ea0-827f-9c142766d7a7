"""
AutoGen编程工作流 - 增强版
实现三个agent的协作：
- Agent1: 代码编写者 (CodeWriter)
- Agent2: 代码审查者 (CodeReviewer)
- Agent3: 代码优化者 (CodeOptimizer)

支持配置管理、日志记录、结果保存等功能
"""

import asyncio
import os
import json
import datetime
from pathlib import Path
from typing import Sequence, Optional, Dict, Any
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.messages import BaseAgentEvent, BaseChatMessage
from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient

# 导入配置管理
try:
    from config import ConfigManager, WorkflowMode, ModelType
    config_manager = ConfigManager()
except ImportError:
    # 如果没有config.py，使用默认配置
    config_manager = None


class ProgrammingWorkflow:
    """编程工作流类，管理三个agent的协作"""

    def __init__(self,
                 model_name: str = "gpt-4o",
                 api_key: str = None,
                 mode: WorkflowMode = None,
                 config: Dict[str, Any] = None):
        """
        初始化工作流

        Args:
            model_name: OpenAI模型名称
            api_key: OpenAI API密钥，如果为None则从环境变量获取
            mode: 工作流模式
            config: 自定义配置字典
        """
        # 加载配置
        if config_manager:
            self.config = config_manager.get_config()
            if mode:
                self.config.mode = mode
        else:
            # 默认配置
            from dataclasses import dataclass
            @dataclass
            class DefaultConfig:
                model_type: str = model_name
                mode: str = "standard"
                max_messages: int = 20
                temperature: float = 0.7
                api_key: str = api_key
                enable_logging: bool = True
                save_results: bool = True
                output_dir: str = "output"
            self.config = DefaultConfig()

        # 应用自定义配置
        if config:
            for key, value in config.items():
                setattr(self.config, key, value)

        # 设置API密钥
        if api_key:
            self.config.api_key = api_key
        elif not self.config.api_key:
            self.config.api_key = os.getenv("OPENAI_API_KEY")

        # 创建模型客户端
        self.model_client = OpenAIChatCompletionClient(
            model=getattr(self.config, 'model_type', model_name),
            api_key=self.config.api_key,
            temperature=getattr(self.config, 'temperature', 0.7)
        )

        # 初始化日志和输出目录
        self._setup_logging()

        # 创建三个专业agent
        self.code_writer = self._create_code_writer()
        self.code_reviewer = self._create_code_reviewer()
        self.code_optimizer = self._create_code_optimizer()

        # 设置终止条件
        self.termination_condition = self._create_termination_condition()

        # 创建团队
        self.team = self._create_team()

        # 会话历史
        self.session_history = []
    
    def _setup_logging(self):
        """设置日志和输出目录"""
        if getattr(self.config, 'save_results', True):
            self.output_dir = Path(getattr(self.config, 'output_dir', 'output'))
            self.output_dir.mkdir(exist_ok=True)

            # 创建会话目录
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            self.session_dir = self.output_dir / f"session_{timestamp}"
            self.session_dir.mkdir(exist_ok=True)

    def _create_code_writer(self) -> AssistantAgent:
        """创建代码编写agent"""
        return AssistantAgent(
            name="CodeWriter",
            model_client=self.model_client,
            description="专业的代码编写者，负责根据需求编写高质量的代码",
            system_message="""
你是一个专业的代码编写者。你的职责是：

1. 仔细分析用户的需求
2. 编写清晰、可读、符合最佳实践的代码
3. 添加适当的注释和文档字符串
4. 考虑代码的可维护性和扩展性
5. 使用合适的设计模式和编程范式

编写代码时请：
- 遵循PEP 8编码规范（如果是Python）
- 使用有意义的变量和函数名
- 添加必要的错误处理
- 包含简单的使用示例

完成代码编写后，请说明你的设计思路和关键实现点。
            """.strip()
        )
    
    def _create_code_reviewer(self) -> AssistantAgent:
        """创建代码审查agent"""
        return AssistantAgent(
            name="CodeReviewer", 
            model_client=self.model_client,
            description="经验丰富的代码审查者，负责检查代码质量并提出改进建议",
            system_message="""
你是一个经验丰富的代码审查者。你的职责是：

1. 仔细审查提供的代码
2. 检查代码的正确性、可读性和性能
3. 识别潜在的bug、安全问题和改进点
4. 提出具体的修改建议
5. 评估代码是否符合最佳实践

审查时请关注：
- 代码逻辑是否正确
- 是否有潜在的边界情况未处理
- 变量命名是否清晰
- 代码结构是否合理
- 是否有重复代码
- 错误处理是否充分
- 性能是否可以优化
- 安全性考虑

请提供详细的审查报告，包括：
- 发现的问题（如果有）
- 具体的改进建议
- 代码质量评分（1-10分）

如果代码质量很好，请说"APPROVE"表示通过审查。
            """.strip()
        )
    
    def _create_code_optimizer(self) -> AssistantAgent:
        """创建代码优化agent"""
        return AssistantAgent(
            name="CodeOptimizer",
            model_client=self.model_client, 
            description="代码优化专家，负责根据审查建议优化代码",
            system_message="""
你是一个代码优化专家。你的职责是：

1. 分析原始代码和审查者的建议
2. 根据建议对代码进行优化和改进
3. 确保优化后的代码功能完整
4. 提高代码的性能、可读性和可维护性
5. 解释优化的原因和效果

优化时请：
- 保持原有功能不变
- 应用审查者的合理建议
- 进一步优化性能和结构
- 添加更好的错误处理
- 改进代码文档
- 使用更高效的算法或数据结构（如适用）

完成优化后，请：
- 展示最终的优化代码
- 说明主要的改进点
- 解释优化带来的好处
- 如果认为代码已经达到最佳状态，请说"OPTIMIZATION_COMPLETE"
            """.strip()
        )
    
    def _create_termination_condition(self):
        """创建终止条件"""
        # 当代码优化完成或达到最大消息数时终止
        text_termination = TextMentionTermination("OPTIMIZATION_COMPLETE")
        max_messages_termination = MaxMessageTermination(max_messages=20)
        return text_termination | max_messages_termination
    
    def _create_team(self) -> SelectorGroupChat:
        """创建agent团队"""
        
        def selector_func(messages: Sequence[BaseAgentEvent | BaseChatMessage]) -> str | None:
            """
            自定义选择器函数，控制agent的执行顺序
            
            工作流程：
            1. 用户输入 -> CodeWriter
            2. CodeWriter完成 -> CodeReviewer  
            3. CodeReviewer完成 -> CodeOptimizer
            4. 如果需要进一步优化，可以循环ReviewerOptimizer
            """
            if not messages:
                return self.code_writer.name
            
            last_message = messages[-1]
            
            # 如果是用户输入，让CodeWriter开始
            if last_message.source == "user":
                return self.code_writer.name
            
            # 如果CodeWriter刚完成，让CodeReviewer审查
            if last_message.source == self.code_writer.name:
                return self.code_reviewer.name
            
            # 如果CodeReviewer完成审查，让CodeOptimizer优化
            if last_message.source == self.code_reviewer.name:
                # 检查是否已经通过审查
                if "APPROVE" in last_message.content.upper():
                    return None  # 审查通过，结束流程
                else:
                    return self.code_optimizer.name
            
            # 如果CodeOptimizer完成优化，可以再次审查
            if last_message.source == self.code_optimizer.name:
                # 检查是否优化完成
                if "OPTIMIZATION_COMPLETE" in last_message.content.upper():
                    return None  # 优化完成，结束流程
                else:
                    return self.code_reviewer.name  # 继续审查优化后的代码
            
            # 默认返回None，让系统自动选择
            return None
        
        return SelectorGroupChat(
            participants=[self.code_writer, self.code_reviewer, self.code_optimizer],
            model_client=self.model_client,
            termination_condition=self.termination_condition,
            selector_func=selector_func
        )
    
    async def run_workflow(self, task: str, save_results: bool = None):
        """
        运行编程工作流

        Args:
            task: 编程任务描述
            save_results: 是否保存结果，None时使用配置默认值
        """
        if save_results is None:
            save_results = getattr(self.config, 'save_results', True)

        print(f"🚀 开始编程工作流...")
        print(f"📝 任务: {task}")
        print(f"⚙️  模式: {getattr(self.config, 'mode', 'standard')}")
        print(f"🤖 模型: {getattr(self.config, 'model_type', 'gpt-4o')}")
        print("="*80)

        # 记录开始时间
        start_time = datetime.datetime.now()

        # 保存任务信息
        task_info = {
            "task": task,
            "start_time": start_time.isoformat(),
            "config": {
                "model": getattr(self.config, 'model_type', 'gpt-4o'),
                "mode": getattr(self.config, 'mode', 'standard'),
                "max_messages": getattr(self.config, 'max_messages', 20)
            }
        }

        messages = []

        try:
            # 运行团队协作
            stream = self.team.run_stream(task=task)

            # 收集消息
            async for message in stream:
                messages.append({
                    "timestamp": datetime.datetime.now().isoformat(),
                    "type": type(message).__name__,
                    "source": getattr(message, 'source', 'unknown'),
                    "content": str(message)
                })
                print(f"[{getattr(message, 'source', 'unknown')}] {message}")

            # 记录结束时间
            end_time = datetime.datetime.now()
            duration = (end_time - start_time).total_seconds()

            print(f"\n✅ 工作流完成！耗时: {duration:.2f}秒")

            # 保存结果
            if save_results and hasattr(self, 'session_dir'):
                await self._save_session_results(task_info, messages, end_time, duration)

        except Exception as e:
            print(f"❌ 工作流执行出错: {e}")
            if save_results and hasattr(self, 'session_dir'):
                error_info = {
                    "error": str(e),
                    "timestamp": datetime.datetime.now().isoformat()
                }
                await self._save_error_log(task_info, error_info)
        finally:
            # 关闭模型客户端
            await self.model_client.close()

    async def _save_session_results(self, task_info: Dict, messages: list, end_time: datetime.datetime, duration: float):
        """保存会话结果"""
        try:
            # 完整的会话记录
            session_data = {
                **task_info,
                "end_time": end_time.isoformat(),
                "duration_seconds": duration,
                "messages": messages,
                "message_count": len(messages)
            }

            # 保存完整会话
            session_file = self.session_dir / "session.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)

            # 提取并保存代码
            await self._extract_and_save_code(messages)

            # 生成摘要
            await self._generate_summary(session_data)

            print(f"📁 结果已保存到: {self.session_dir}")

        except Exception as e:
            print(f"⚠️  保存结果时出错: {e}")

    async def _extract_and_save_code(self, messages: list):
        """从消息中提取并保存代码"""
        code_blocks = []

        for msg in messages:
            content = msg.get('content', '')
            # 简单的代码块提取（可以改进）
            if '```python' in content or '```' in content:
                lines = content.split('\n')
                in_code_block = False
                current_code = []

                for line in lines:
                    if line.strip().startswith('```'):
                        if in_code_block:
                            # 代码块结束
                            if current_code:
                                code_blocks.append({
                                    'source': msg.get('source', 'unknown'),
                                    'timestamp': msg.get('timestamp'),
                                    'code': '\n'.join(current_code)
                                })
                                current_code = []
                            in_code_block = False
                        else:
                            # 代码块开始
                            in_code_block = True
                    elif in_code_block:
                        current_code.append(line)

        # 保存提取的代码
        if code_blocks:
            for i, block in enumerate(code_blocks):
                code_file = self.session_dir / f"code_{i+1}_{block['source']}.py"
                with open(code_file, 'w', encoding='utf-8') as f:
                    f.write(f"# Generated by {block['source']}\n")
                    f.write(f"# Timestamp: {block['timestamp']}\n\n")
                    f.write(block['code'])

    async def _generate_summary(self, session_data: Dict):
        """生成会话摘要"""
        summary = {
            "task": session_data['task'],
            "duration": f"{session_data['duration_seconds']:.2f} seconds",
            "message_count": session_data['message_count'],
            "participants": list(set(msg.get('source', 'unknown') for msg in session_data['messages'])),
            "start_time": session_data['start_time'],
            "end_time": session_data['end_time']
        }

        summary_file = self.session_dir / "summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

    async def _save_error_log(self, task_info: Dict, error_info: Dict):
        """保存错误日志"""
        error_data = {
            **task_info,
            **error_info
        }

        error_file = self.session_dir / "error.json"
        with open(error_file, 'w', encoding='utf-8') as f:
            json.dump(error_data, f, indent=2, ensure_ascii=False)
    
    async def reset_workflow(self):
        """重置工作流状态"""
        await self.team.reset()


async def main():
    """主函数示例"""
    # 创建工作流实例
    workflow = ProgrammingWorkflow()
    
    # 示例任务
    task = """
请编写一个Python函数，实现以下功能：
1. 接收一个整数列表作为输入
2. 返回列表中所有偶数的平方和
3. 如果列表为空或没有偶数，返回0
4. 需要处理异常情况

要求：代码要清晰、高效，并包含适当的文档和错误处理。
    """
    
    # 运行工作流
    await workflow.run_workflow(task)


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
