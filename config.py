"""
AutoGen编程工作流配置文件
包含各种配置选项和预设模板
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Optional
from enum import Enum


class ModelType(Enum):
    """支持的模型类型"""
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"
    GPT_4_TURBO = "gpt-4-turbo"
    GPT_3_5_TURBO = "gpt-3.5-turbo"


class WorkflowMode(Enum):
    """工作流模式"""
    STANDARD = "standard"      # 标准模式：编写->审查->优化
    FAST = "fast"             # 快速模式：编写->审查
    THOROUGH = "thorough"     # 深度模式：多轮优化
    COLLABORATIVE = "collaborative"  # 协作模式：所有agent参与讨论


@dataclass
class AgentConfig:
    """Agent配置"""
    name: str
    description: str
    system_message: str
    temperature: float = 0.7
    max_tokens: Optional[int] = None


@dataclass
class WorkflowConfig:
    """工作流配置"""
    model_type: ModelType = ModelType.GPT_4O
    mode: WorkflowMode = WorkflowMode.STANDARD
    max_messages: int = 20
    temperature: float = 0.7
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    org_id: Optional[str] = None
    enable_logging: bool = True
    save_results: bool = True
    output_dir: str = "output"


class SystemMessages:
    """系统消息模板"""
    
    CODE_WRITER_STANDARD = """
你是一个专业的代码编写者。你的职责是：

1. 仔细分析用户的需求
2. 编写清晰、可读、符合最佳实践的代码
3. 添加适当的注释和文档字符串
4. 考虑代码的可维护性和扩展性
5. 使用合适的设计模式和编程范式

编写代码时请：
- 遵循相应语言的编码规范
- 使用有意义的变量和函数名
- 添加必要的错误处理
- 包含简单的使用示例

完成代码编写后，请说明你的设计思路和关键实现点。
    """.strip()
    
    CODE_WRITER_FAST = """
你是一个高效的代码编写者。专注于快速实现功能需求：

1. 快速理解需求核心
2. 编写简洁有效的代码
3. 确保代码功能正确
4. 添加基本注释

重点关注：
- 功能实现的正确性
- 代码的简洁性
- 基本的错误处理
    """.strip()
    
    CODE_REVIEWER_STANDARD = """
你是一个经验丰富的代码审查者。你的职责是：

1. 仔细审查提供的代码
2. 检查代码的正确性、可读性和性能
3. 识别潜在的bug、安全问题和改进点
4. 提出具体的修改建议
5. 评估代码是否符合最佳实践

审查时请关注：
- 代码逻辑是否正确
- 是否有潜在的边界情况未处理
- 变量命名是否清晰
- 代码结构是否合理
- 是否有重复代码
- 错误处理是否充分
- 性能是否可以优化
- 安全性考虑

请提供详细的审查报告，包括：
- 发现的问题（如果有）
- 具体的改进建议
- 代码质量评分（1-10分）

如果代码质量很好，请说"APPROVE"表示通过审查。
    """.strip()
    
    CODE_REVIEWER_FAST = """
你是一个高效的代码审查者。快速检查代码质量：

1. 检查代码功能是否正确
2. 识别明显的问题
3. 提出关键改进建议

重点关注：
- 功能正确性
- 明显的bug
- 关键的性能问题

如果代码基本可用，请说"APPROVE"。
    """.strip()
    
    CODE_OPTIMIZER_STANDARD = """
你是一个代码优化专家。你的职责是：

1. 分析原始代码和审查者的建议
2. 根据建议对代码进行优化和改进
3. 确保优化后的代码功能完整
4. 提高代码的性能、可读性和可维护性
5. 解释优化的原因和效果

优化时请：
- 保持原有功能不变
- 应用审查者的合理建议
- 进一步优化性能和结构
- 添加更好的错误处理
- 改进代码文档
- 使用更高效的算法或数据结构（如适用）

完成优化后，请：
- 展示最终的优化代码
- 说明主要的改进点
- 解释优化带来的好处
- 如果认为代码已经达到最佳状态，请说"OPTIMIZATION_COMPLETE"
    """.strip()


class TaskTemplates:
    """任务模板"""
    
    ALGORITHM_IMPLEMENTATION = """
实现{algorithm_name}算法，要求：

1. 算法功能：{description}
2. 时间复杂度：{time_complexity}
3. 空间复杂度：{space_complexity}
4. 包含完整的错误处理
5. 添加详细的文档字符串
6. 包含测试用例

要求：
- 代码要高效且易读
- 包含边界情况处理
- 添加使用示例
    """
    
    DATA_STRUCTURE = """
实现{data_structure_name}数据结构，要求：

1. 核心操作：{operations}
2. 支持的方法：{methods}
3. 时间复杂度分析
4. 内存使用优化
5. 完整的类型提示
6. 详细的文档

要求：
- 遵循面向对象设计原则
- 包含完整的错误处理
- 添加使用示例和测试
    """
    
    WEB_API = """
使用{framework}设计{api_name} API，包含：

1. 数据模型：{models}
2. API端点：{endpoints}
3. 数据验证和错误处理
4. 认证和授权（如需要）
5. API文档和响应模型

要求：
- 遵循RESTful设计原则
- 包含适当的HTTP状态码
- 添加请求/响应模型验证
- 包含API使用示例
    """


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config = WorkflowConfig()
        self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        self.config.api_key = os.getenv("OPENAI_API_KEY")
        self.config.base_url = os.getenv("OPENAI_BASE_URL")
        self.config.org_id = os.getenv("OPENAI_ORG_ID")

        # 模型配置
        model_name = os.getenv("AUTOGEN_MODEL", "gpt-4o")
        try:
            self.config.model_type = ModelType(model_name)
        except ValueError:
            self.config.model_type = ModelType.GPT_4O

        # 其他配置
        self.config.max_messages = int(os.getenv("AUTOGEN_MAX_MESSAGES", "20"))
        self.config.temperature = float(os.getenv("AUTOGEN_TEMPERATURE", "0.7"))
        self.config.enable_logging = os.getenv("AUTOGEN_LOGGING", "true").lower() == "true"
        self.config.output_dir = os.getenv("AUTOGEN_OUTPUT_DIR", "output")
    
    def get_agent_config(self, agent_name: str, mode: WorkflowMode = WorkflowMode.STANDARD) -> AgentConfig:
        """获取Agent配置"""
        configs = {
            "CodeWriter": {
                WorkflowMode.STANDARD: AgentConfig(
                    name="CodeWriter",
                    description="专业的代码编写者，负责根据需求编写高质量的代码",
                    system_message=SystemMessages.CODE_WRITER_STANDARD,
                    temperature=0.7
                ),
                WorkflowMode.FAST: AgentConfig(
                    name="CodeWriter",
                    description="高效的代码编写者，快速实现功能需求",
                    system_message=SystemMessages.CODE_WRITER_FAST,
                    temperature=0.5
                )
            },
            "CodeReviewer": {
                WorkflowMode.STANDARD: AgentConfig(
                    name="CodeReviewer",
                    description="经验丰富的代码审查者，负责检查代码质量并提出改进建议",
                    system_message=SystemMessages.CODE_REVIEWER_STANDARD,
                    temperature=0.3
                ),
                WorkflowMode.FAST: AgentConfig(
                    name="CodeReviewer",
                    description="高效的代码审查者，快速检查代码质量",
                    system_message=SystemMessages.CODE_REVIEWER_FAST,
                    temperature=0.3
                )
            },
            "CodeOptimizer": {
                WorkflowMode.STANDARD: AgentConfig(
                    name="CodeOptimizer",
                    description="代码优化专家，负责根据审查建议优化代码",
                    system_message=SystemMessages.CODE_OPTIMIZER_STANDARD,
                    temperature=0.5
                )
            }
        }
        
        return configs.get(agent_name, {}).get(mode, configs[agent_name][WorkflowMode.STANDARD])
    
    def create_task_from_template(self, template_name: str, **kwargs) -> str:
        """从模板创建任务"""
        templates = {
            "algorithm": TaskTemplates.ALGORITHM_IMPLEMENTATION,
            "data_structure": TaskTemplates.DATA_STRUCTURE,
            "web_api": TaskTemplates.WEB_API
        }
        
        template = templates.get(template_name, "")
        return template.format(**kwargs)
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
    
    def get_config(self) -> WorkflowConfig:
        """获取当前配置"""
        return self.config


# 全局配置实例
config_manager = ConfigManager()
