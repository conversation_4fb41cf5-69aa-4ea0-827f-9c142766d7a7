#!/usr/bin/env python3
"""
使用标准OpenAI API的演示
"""

import asyncio
import os
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient

async def demo_with_standard_openai():
    """使用标准OpenAI API的演示"""
    
    # 检查API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请设置OPENAI_API_KEY环境变量")
        return
    
    print("🤖 AutoGen编程工作流演示 (使用标准OpenAI API)")
    print("="*60)
    
    # 创建模型客户端 - 使用标准OpenAI API
    model_client = OpenAIChatCompletionClient(
        model="gpt-3.5-turbo",
        api_key=api_key,
        temperature=0.7
    )
    
    # 创建代码编写agent
    code_writer = AssistantAgent(
        name="CodeWriter",
        model_client=model_client,
        description="专业的代码编写者",
        system_message="""
你是一个专业的Python代码编写者。请根据用户需求编写高质量的代码。

要求：
1. 代码要清晰易读
2. 包含适当的注释
3. 添加类型提示
4. 包含文档字符串
5. 处理基本的错误情况

完成后请简要说明你的实现思路。
        """.strip()
    )
    
    # 创建代码审查agent
    code_reviewer = AssistantAgent(
        name="CodeReviewer",
        model_client=model_client,
        description="代码审查者",
        system_message="""
你是一个经验丰富的代码审查者。请仔细检查提供的代码：

1. 检查代码逻辑是否正确
2. 查看是否有潜在问题
3. 评估代码质量和可读性
4. 提出具体的改进建议

如果代码质量良好且没有明显问题，请说"APPROVE"表示通过审查。
        """.strip()
    )
    
    # 设置终止条件
    termination = TextMentionTermination("APPROVE") | MaxMessageTermination(8)
    
    # 创建团队
    team = RoundRobinGroupChat(
        [code_writer, code_reviewer],
        termination_condition=termination
    )
    
    # 示例任务
    task = """
请编写一个Python函数fibonacci(n)，计算第n个斐波那契数。

要求：
1. 使用递归实现
2. 包含类型提示
3. 添加文档字符串
4. 处理边界情况（n <= 0）
5. 添加简单的使用示例
    """
    
    print(f"📝 任务: {task}")
    print("="*60)
    
    try:
        # 运行团队协作
        stream = team.run_stream(task=task)
        await Console(stream)
        print("\n✅ 工作流完成！")
        
    except Exception as e:
        print(f"❌ 工作流执行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭模型客户端
        await model_client.close()

async def demo_simple_task():
    """简单任务演示"""
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请设置OPENAI_API_KEY环境变量")
        return
    
    print("🎯 简单任务演示")
    print("="*40)
    
    # 创建单个agent
    model_client = OpenAIChatCompletionClient(
        model="gpt-3.5-turbo",
        api_key=api_key,
        temperature=0.5
    )
    
    agent = AssistantAgent(
        name="Programmer",
        model_client=model_client,
        description="程序员",
        system_message="你是一个Python程序员。请编写简洁、清晰的代码。"
    )
    
    # 简单任务
    task = "编写一个Python函数add(a, b)，返回两个数的和，包含类型提示和文档字符串。"
    
    try:
        # 单agent团队
        team = RoundRobinGroupChat(
            [agent],
            termination_condition=MaxMessageTermination(2)
        )
        
        stream = team.run_stream(task=task)
        await Console(stream)
        print("\n✅ 简单任务完成！")
        
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await model_client.close()

def main():
    """主函数"""
    print("请选择演示模式:")
    print("1. 完整工作流演示 (代码编写 + 审查)")
    print("2. 简单任务演示 (单个agent)")
    
    choice = input("请选择 (1-2, 默认1): ").strip() or "1"
    
    if choice == "1":
        asyncio.run(demo_with_standard_openai())
    elif choice == "2":
        asyncio.run(demo_simple_task())
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
