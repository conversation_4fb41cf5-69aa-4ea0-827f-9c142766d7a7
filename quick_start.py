#!/usr/bin/env python3
"""
AutoGen编程工作流快速启动脚本
提供简单的命令行界面来运行工作流
"""

import asyncio
import argparse
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

try:
    from autogen_programming_workflow import ProgrammingWorkflow
    from config import WorkflowMode, ModelType, config_manager
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所需依赖: pip install -r requirements.txt")
    sys.exit(1)


def setup_environment():
    """设置环境"""
    # 尝试加载.env文件
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ 已加载.env配置文件")
    except ImportError:
        print("⚠️  python-dotenv未安装，跳过.env文件加载")
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 未找到OPENAI_API_KEY环境变量")
        print("请设置API密钥:")
        print("  export OPENAI_API_KEY='your-api-key'")
        print("或创建.env文件（参考.env.example）")
        return False
    
    return True


async def run_interactive_mode():
    """交互模式"""
    print("🤖 AutoGen编程工作流 - 交互模式")
    print("="*50)
    
    # 配置选择
    print("\n📋 选择配置:")
    print("1. 标准模式 (完整的编写->审查->优化流程)")
    print("2. 快速模式 (编写->审查)")
    print("3. 自定义配置")
    
    mode_choice = input("请选择模式 (1-3, 默认1): ").strip() or "1"
    
    mode = WorkflowMode.STANDARD
    model = "gpt-4o-mini"  # 默认使用较便宜的模型
    
    if mode_choice == "2":
        mode = WorkflowMode.FAST
    elif mode_choice == "3":
        print("\n⚙️  自定义配置:")
        model = input(f"模型名称 (默认{model}): ").strip() or model
        
        mode_options = {
            "1": WorkflowMode.STANDARD,
            "2": WorkflowMode.FAST,
        }
        mode_input = input("模式 (1=标准, 2=快速, 默认1): ").strip() or "1"
        mode = mode_options.get(mode_input, WorkflowMode.STANDARD)
    
    print(f"\n🔧 配置:")
    print(f"  模式: {mode.value if hasattr(mode, 'value') else mode}")
    print(f"  模型: {model}")
    
    # 创建工作流
    try:
        workflow = ProgrammingWorkflow(
            model_name=model,
            mode=mode
        )
    except Exception as e:
        print(f"❌ 创建工作流失败: {e}")
        return
    
    # 任务输入
    print("\n📝 请输入编程任务:")
    print("(输入多行时，以空行结束)")
    
    task_lines = []
    while True:
        line = input()
        if not line.strip() and task_lines:
            break
        task_lines.append(line)
    
    task = "\n".join(task_lines).strip()
    
    if not task:
        print("❌ 任务不能为空")
        return
    
    print(f"\n🚀 开始执行任务...")
    
    # 运行工作流
    try:
        await workflow.run_workflow(task)
        print("\n✅ 任务完成！")
    except Exception as e:
        print(f"❌ 执行失败: {e}")


async def run_batch_mode(task_file: str, output_dir: str = None):
    """批处理模式"""
    print(f"📁 批处理模式: {task_file}")
    
    if not Path(task_file).exists():
        print(f"❌ 任务文件不存在: {task_file}")
        return
    
    # 读取任务
    with open(task_file, 'r', encoding='utf-8') as f:
        task = f.read().strip()
    
    if not task:
        print("❌ 任务文件为空")
        return
    
    # 配置
    config = {}
    if output_dir:
        config['output_dir'] = output_dir
    
    # 创建工作流
    workflow = ProgrammingWorkflow(config=config)
    
    # 运行
    await workflow.run_workflow(task)


async def run_demo():
    """演示模式"""
    print("🎯 AutoGen编程工作流演示")
    print("="*40)
    
    demo_tasks = [
        {
            "name": "简单函数",
            "task": """
编写一个Python函数fibonacci(n)，要求：
1. 计算第n个斐波那契数
2. 使用递归实现
3. 添加缓存优化
4. 包含错误处理
5. 添加类型提示和文档
            """.strip()
        },
        {
            "name": "数据处理类",
            "task": """
创建一个Python类TextProcessor，要求：
1. 初始化时接收文本内容
2. 提供方法统计单词数量
3. 提供方法查找特定单词
4. 提供方法替换文本
5. 支持链式调用
6. 包含完整的错误处理
            """.strip()
        }
    ]
    
    print("可用演示:")
    for i, demo in enumerate(demo_tasks, 1):
        print(f"  {i}. {demo['name']}")
    
    choice = input(f"选择演示 (1-{len(demo_tasks)}, 默认1): ").strip() or "1"
    
    try:
        demo_index = int(choice) - 1
        if 0 <= demo_index < len(demo_tasks):
            demo = demo_tasks[demo_index]
            print(f"\n🎯 运行演示: {demo['name']}")
            
            workflow = ProgrammingWorkflow(model_name="gpt-4o-mini")
            await workflow.run_workflow(demo['task'])
        else:
            print("❌ 无效选择")
    except ValueError:
        print("❌ 无效输入")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AutoGen编程工作流快速启动",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python quick_start.py                    # 交互模式
  python quick_start.py --demo             # 演示模式
  python quick_start.py --batch task.txt   # 批处理模式
  python quick_start.py --test             # 运行测试
        """
    )
    
    parser.add_argument("--demo", action="store_true", help="运行演示")
    parser.add_argument("--batch", metavar="FILE", help="批处理模式，从文件读取任务")
    parser.add_argument("--output", metavar="DIR", help="输出目录")
    parser.add_argument("--test", action="store_true", help="运行测试")
    
    args = parser.parse_args()
    
    # 设置环境
    if not setup_environment():
        sys.exit(1)
    
    # 根据参数选择模式
    if args.test:
        print("🧪 运行测试...")
        from test_workflow import run_all_tests
        asyncio.run(run_all_tests())
    elif args.demo:
        asyncio.run(run_demo())
    elif args.batch:
        asyncio.run(run_batch_mode(args.batch, args.output))
    else:
        asyncio.run(run_interactive_mode())


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)
