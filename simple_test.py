#!/usr/bin/env python3
"""
简单测试脚本
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载.env配置文件")
except ImportError:
    print("⚠️  python-dotenv未安装")

# 检查API配置
api_key = os.getenv("OPENAI_API_KEY")
base_url = os.getenv("OPENAI_BASE_URL")

print(f"API Key: {api_key[:10]}..." if api_key else "API Key: 未设置")
print(f"Base URL: {base_url}")

# 测试导入
try:
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    print("✅ 成功导入 OpenAIChatCompletionClient")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

async def test_client():
    """测试创建客户端"""
    try:
        client_kwargs = {
            "model": "gpt-4o-mini",
            "api_key": api_key,
            "temperature": 0.7
        }

        if base_url:
            client_kwargs["base_url"] = base_url

        print(f"创建客户端参数: {client_kwargs}")

        client = OpenAIChatCompletionClient(**client_kwargs)
        print("✅ 成功创建模型客户端")

        # 关闭客户端
        await client.close()
        print("✅ 成功关闭客户端")

    except Exception as e:
        import traceback
        print(f"❌ 创建客户端失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_client())
