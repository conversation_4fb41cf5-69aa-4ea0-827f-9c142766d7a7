#!/usr/bin/env python3
"""
简化版AutoGen编程工作流
专门为第三方API优化
"""

import asyncio
import os
from dotenv import load_dotenv
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient

# 加载环境变量
load_dotenv()

class SimpleWorkflow:
    """简化版编程工作流"""
    
    def __init__(self):
        # 获取配置
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL")
        
        if not api_key:
            raise ValueError("请设置OPENAI_API_KEY环境变量")
        
        # 创建模型客户端
        client_kwargs = {
            "model": "gpt-3.5-turbo",  # 使用更通用的模型名
            "api_key": api_key,
            "temperature": 0.7
        }
        
        if base_url:
            client_kwargs["base_url"] = base_url
            print(f"🔗 使用自定义API地址: {base_url}")
        
        self.model_client = OpenAIChatCompletionClient(**client_kwargs)
        
        # 创建agents
        self.code_writer = AssistantAgent(
            name="CodeWriter",
            model_client=self.model_client,
            description="专业的代码编写者",
            system_message="""
你是一个专业的代码编写者。请根据用户需求编写高质量的代码。

要求：
1. 代码要清晰易读
2. 包含适当的注释
3. 添加类型提示（如果是Python）
4. 包含文档字符串
5. 处理基本的错误情况

完成后请说明你的实现思路。
            """.strip()
        )
        
        self.code_reviewer = AssistantAgent(
            name="CodeReviewer",
            model_client=self.model_client,
            description="代码审查者",
            system_message="""
你是一个代码审查者。请仔细检查提供的代码：

1. 检查代码逻辑是否正确
2. 查看是否有潜在问题
3. 评估代码质量
4. 提出改进建议

如果代码质量良好，请说"APPROVE"表示通过审查。
            """.strip()
        )
        
        # 设置终止条件
        termination = TextMentionTermination("APPROVE") | MaxMessageTermination(10)
        
        # 创建团队
        self.team = RoundRobinGroupChat(
            [self.code_writer, self.code_reviewer],
            termination_condition=termination
        )
    
    async def run(self, task: str):
        """运行工作流"""
        print(f"🚀 开始编程工作流")
        print(f"📝 任务: {task}")
        print("="*60)
        
        try:
            # 运行团队协作
            stream = self.team.run_stream(task=task)
            await Console(stream)
            print("\n✅ 工作流完成！")
            
        except Exception as e:
            print(f"❌ 工作流执行出错: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 关闭模型客户端
            await self.model_client.close()

async def main():
    """主函数"""
    # 创建工作流
    workflow = SimpleWorkflow()
    
    # 示例任务
    task = """
请编写一个Python函数calculate_area(length, width)，计算矩形面积。

要求：
1. 包含类型提示
2. 添加文档字符串
3. 验证输入参数（必须为正数）
4. 返回计算结果
    """
    
    # 运行工作流
    await workflow.run(task)

if __name__ == "__main__":
    asyncio.run(main())
