#!/usr/bin/env python3
"""
Moonshot API测试脚本
"""

import asyncio
import os
from dotenv import load_dotenv
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import MaxMessageTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import <PERSON>sole
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.models.openai._model_info import ModelInfo

# 加载环境变量
load_dotenv()

async def test_moonshot_api():
    """测试Moonshot API"""
    
    # 获取配置
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    model_name = os.getenv("AUTOGEN_MODEL", "moonshot-v1-8k")
    
    print(f"🔑 API Key: {api_key[:10]}..." if api_key else "❌ 未设置API Key")
    print(f"🔗 Base URL: {base_url}")
    print(f"🤖 Model: {model_name}")
    print("="*50)
    
    if not api_key:
        print("❌ 请设置OPENAI_API_KEY环境变量")
        return
    
    try:
        # 创建模型客户端
        client_kwargs = {
            "model": model_name,
            "api_key": api_key,
            "temperature": 0.7
        }
        
        if base_url:
            client_kwargs["base_url"] = base_url
            # 为Moonshot模型添加模型信息
            client_kwargs["model_info"] = ModelInfo(
                family="moonshot",
                vision=False,
                function_calling=True,
                json_output=True,
                structured_output=False
            )
        
        print("🔧 正在创建模型客户端...")
        model_client = OpenAIChatCompletionClient(**client_kwargs)
        print("✅ 模型客户端创建成功")
        
        # 创建一个简单的agent
        agent = AssistantAgent(
            name="TestAgent",
            model_client=model_client,
            description="测试agent",
            system_message="你是一个Python编程助手。请简洁地回答问题。"
        )
        
        print("✅ Agent创建成功")
        
        # 创建简单的团队
        team = RoundRobinGroupChat(
            [agent],
            termination_condition=MaxMessageTermination(2)
        )
        
        print("✅ 团队创建成功")
        
        # 测试任务
        task = "请编写一个简单的Python函数add(a, b)，返回两个数的和。"
        
        print(f"\n📝 测试任务: {task}")
        print("🚀 开始执行...")
        print("="*50)
        
        # 运行测试
        stream = team.run_stream(task=task)
        await Console(stream)
        
        print("\n✅ 测试完成！")
        
        # 关闭客户端
        await model_client.close()
        
    except Exception as e:
        import traceback
        print(f"❌ 测试失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_moonshot_api())
