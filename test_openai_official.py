#!/usr/bin/env python3
"""
使用OpenAI官方API测试工作流
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

async def test_with_openai():
    """使用OpenAI官方API测试"""
    print("🧪 使用OpenAI官方API测试工作流")
    
    # 临时设置为OpenAI官方API
    original_base_url = os.getenv("OPENAI_BASE_URL")
    os.environ["OPENAI_BASE_URL"] = "https://api.openai.com/v1"
    
    try:
        from autogen_programming_workflow import ProgrammingWorkflow
        
        # 创建工作流
        workflow = ProgrammingWorkflow(model_name="gpt-3.5-turbo")
        print("✅ 成功创建工作流")
        
        # 简单任务
        task = """
编写一个Python函数multiply(a, b)，返回两个数的乘积。
要求：
1. 包含类型提示
2. 添加文档字符串
3. 处理异常情况
        """
        
        print("🚀 开始运行工作流...")
        await workflow.run_workflow(task)
        print("✅ 工作流完成")
        
    except Exception as e:
        import traceback
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
    finally:
        # 恢复原始设置
        if original_base_url:
            os.environ["OPENAI_BASE_URL"] = original_base_url
        else:
            os.environ.pop("OPENAI_BASE_URL", None)

if __name__ == "__main__":
    # 检查是否有OpenAI API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 请设置OPENAI_API_KEY环境变量")
        sys.exit(1)
    
    asyncio.run(test_with_openai())
