#!/usr/bin/env python3
"""
简单工作流测试
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 已加载.env配置文件")
except ImportError:
    print("⚠️  python-dotenv未安装")

async def test_workflow():
    """测试工作流"""
    print("开始测试工作流...")

    try:
        print("正在导入 ProgrammingWorkflow...")
        from autogen_programming_workflow import ProgrammingWorkflow
        print("✅ 成功导入 ProgrammingWorkflow")

        print("正在创建工作流...")
        # 创建工作流
        workflow = ProgrammingWorkflow(model_name="gpt-4o-mini")
        print("✅ 成功创建工作流")

        # 简单任务
        task = """
编写一个Python函数add(a, b)，返回两个数的和。
要求：
1. 包含类型提示
2. 添加文档字符串
        """

        print("🚀 开始运行工作流...")
        await workflow.run_workflow(task)
        print("✅ 工作流完成")

    except Exception as e:
        import traceback
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_workflow())
