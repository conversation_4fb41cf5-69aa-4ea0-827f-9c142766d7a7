"""
AutoGen编程工作流测试文件
用于测试和验证工作流功能
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

try:
    from autogen_programming_workflow import ProgrammingWorkflow
    from config import WorkflowMode, ModelType
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所需依赖: pip install -r requirements.txt")
    sys.exit(1)


async def test_basic_workflow():
    """测试基本工作流"""
    print("🧪 测试基本工作流")
    print("="*50)
    
    # 检查API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请设置OPENAI_API_KEY环境变量")
        return False
    
    try:
        # 创建工作流
        workflow = ProgrammingWorkflow(
            model_name="gpt-4o-mini",  # 使用较便宜的模型进行测试
            api_key=api_key
        )
        
        # 简单的测试任务
        task = """
编写一个Python函数add_numbers(a, b)，要求：
1. 接收两个数字参数
2. 返回它们的和
3. 包含类型提示
4. 添加文档字符串
5. 处理异常情况
        """
        
        print(f"📝 测试任务: {task}")
        
        # 运行工作流
        await workflow.run_workflow(task)
        
        print("✅ 基本工作流测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_fast_mode():
    """测试快速模式"""
    print("\n🧪 测试快速模式")
    print("="*50)
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请设置OPENAI_API_KEY环境变量")
        return False
    
    try:
        # 创建快速模式工作流
        workflow = ProgrammingWorkflow(
            model_name="gpt-4o-mini",
            api_key=api_key,
            mode=WorkflowMode.FAST if 'WorkflowMode' in globals() else None
        )
        
        task = """
编写一个简单的计算器函数calculator(operation, a, b)，支持加减乘除四种运算。
        """
        
        await workflow.run_workflow(task)
        
        print("✅ 快速模式测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 快速模式测试失败: {e}")
        return False


async def test_configuration():
    """测试配置功能"""
    print("\n🧪 测试配置功能")
    print("="*50)
    
    try:
        # 测试自定义配置
        custom_config = {
            "max_messages": 10,
            "temperature": 0.5,
            "save_results": True,
            "output_dir": "test_output"
        }
        
        workflow = ProgrammingWorkflow(
            model_name="gpt-4o-mini",
            config=custom_config
        )
        
        print(f"✅ 配置测试完成")
        print(f"   - 最大消息数: {getattr(workflow.config, 'max_messages', 'N/A')}")
        print(f"   - 温度: {getattr(workflow.config, 'temperature', 'N/A')}")
        print(f"   - 输出目录: {getattr(workflow.config, 'output_dir', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


async def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理")
    print("="*50)
    
    try:
        # 使用无效的API密钥测试错误处理
        workflow = ProgrammingWorkflow(
            model_name="gpt-4o-mini",
            api_key="invalid_key"
        )
        
        task = "编写一个简单的Hello World程序"
        
        # 这应该会失败，但不应该崩溃
        await workflow.run_workflow(task)
        
        print("✅ 错误处理测试完成")
        return True
        
    except Exception as e:
        print(f"✅ 错误处理正常工作: {e}")
        return True


def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_packages = [
        "autogen_agentchat",
        "autogen_core", 
        "autogen_ext",
        "openai"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖项已安装")
    return True


async def run_all_tests():
    """运行所有测试"""
    print("🚀 AutoGen编程工作流测试套件")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("\n⚠️  警告: 未设置OPENAI_API_KEY环境变量")
        print("某些测试可能会失败")
    
    tests = [
        ("配置功能", test_configuration),
        ("错误处理", test_error_handling),
    ]
    
    # 只有在有API密钥时才运行需要API的测试
    if os.getenv("OPENAI_API_KEY"):
        tests.extend([
            ("基本工作流", test_basic_workflow),
            ("快速模式", test_fast_mode),
        ])
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 测试结果摘要
    print(f"\n{'='*60}")
    print("📊 测试结果摘要")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(run_all_tests())
