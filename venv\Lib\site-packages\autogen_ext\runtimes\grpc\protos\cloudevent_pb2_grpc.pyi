"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*
CloudEvent Protobuf Format

- Required context attributes are explicitly represented.
- Optional and Extension context attributes are carried in a map structure.
- Data may be represented as binary, text, or protobuf messages.
"""

import abc
import collections.abc
import grpc
import grpc.aio
import typing

_T = typing.TypeVar("_T")

class _MaybeAsyncIterator(collections.abc.AsyncIterator[_T], collections.abc.Iterator[_T], metaclass=abc.ABCMeta): ...

class _ServicerContext(grpc.ServicerContext, grpc.aio.ServicerContext):  # type: ignore[misc, type-arg]
    ...
