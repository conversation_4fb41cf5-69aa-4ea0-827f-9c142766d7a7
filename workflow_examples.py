"""
AutoGen编程工作流使用示例
展示不同类型的编程任务和工作流配置
"""

import asyncio
import os
from autogen_programming_workflow import ProgrammingWorkflow


class WorkflowExamples:
    """工作流示例集合"""
    
    def __init__(self):
        # 从环境变量获取API密钥，或者在这里直接设置
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("⚠️  请设置OPENAI_API_KEY环境变量或在代码中提供API密钥")
        
        self.workflow = ProgrammingWorkflow(api_key=api_key)
    
    async def example_1_data_processing(self):
        """示例1：数据处理函数"""
        task = """
编写一个Python类DataProcessor，实现以下功能：

1. 初始化时接收一个数据列表
2. 提供方法filter_data(condition)，根据条件过滤数据
3. 提供方法transform_data(func)，对数据应用转换函数
4. 提供方法aggregate_data(operation)，对数据进行聚合操作（sum, avg, max, min）
5. 支持方法链式调用
6. 包含完整的错误处理和类型提示

要求：
- 使用现代Python特性（类型提示、dataclass等）
- 代码要有良好的文档
- 包含使用示例
        """
        
        print("🔄 运行示例1：数据处理类")
        await self.workflow.run_workflow(task)
        await self.workflow.reset_workflow()
    
    async def example_2_algorithm_implementation(self):
        """示例2：算法实现"""
        task = """
实现一个高效的二分查找算法，要求：

1. 支持在有序列表中查找目标值
2. 返回目标值的索引，如果不存在返回-1
3. 支持自定义比较函数
4. 包含递归和迭代两种实现方式
5. 添加详细的时间复杂度和空间复杂度分析
6. 包含完整的单元测试用例

要求：
- 代码要高效且易读
- 包含边界情况处理
- 添加性能基准测试
        """
        
        print("🔄 运行示例2：算法实现")
        await self.workflow.run_workflow(task)
        await self.workflow.reset_workflow()
    
    async def example_3_web_api(self):
        """示例3：Web API设计"""
        task = """
使用FastAPI设计一个简单的用户管理API，包含：

1. 用户模型（User）：id, name, email, created_at
2. API端点：
   - POST /users - 创建用户
   - GET /users - 获取所有用户
   - GET /users/{user_id} - 获取特定用户
   - PUT /users/{user_id} - 更新用户
   - DELETE /users/{user_id} - 删除用户
3. 数据验证和错误处理
4. 内存存储（使用字典模拟数据库）
5. API文档和响应模型

要求：
- 遵循RESTful设计原则
- 包含适当的HTTP状态码
- 添加请求/响应模型验证
- 包含API使用示例
        """
        
        print("🔄 运行示例3：Web API设计")
        await self.workflow.run_workflow(task)
        await self.workflow.reset_workflow()
    
    async def example_4_design_pattern(self):
        """示例4：设计模式实现"""
        task = """
实现观察者模式（Observer Pattern），要求：

1. 创建Subject（主题）基类
2. 创建Observer（观察者）接口
3. 实现ConcreteSubject（具体主题）
4. 实现ConcreteObserver（具体观察者）
5. 支持动态添加/移除观察者
6. 当主题状态改变时，自动通知所有观察者
7. 包含实际应用场景示例（如新闻订阅系统）

要求：
- 使用Python的ABC模块定义接口
- 代码要体现设计模式的核心思想
- 包含完整的使用示例和测试
        """
        
        print("🔄 运行示例4：设计模式实现")
        await self.workflow.run_workflow(task)
        await self.workflow.reset_workflow()
    
    async def run_all_examples(self):
        """运行所有示例"""
        examples = [
            self.example_1_data_processing,
            self.example_2_algorithm_implementation,
            self.example_3_web_api,
            self.example_4_design_pattern
        ]
        
        for i, example in enumerate(examples, 1):
            print(f"\n{'='*80}")
            print(f"🎯 开始运行示例 {i}")
            print(f"{'='*80}")
            
            try:
                await example()
                print(f"✅ 示例 {i} 完成")
            except Exception as e:
                print(f"❌ 示例 {i} 执行失败: {e}")
            
            print(f"{'='*80}\n")
    
    async def custom_task(self, task_description: str):
        """运行自定义任务"""
        print(f"🎯 运行自定义任务")
        await self.workflow.run_workflow(task_description)


async def interactive_mode():
    """交互模式"""
    examples = WorkflowExamples()
    
    while True:
        print("\n🤖 AutoGen编程工作流")
        print("="*50)
        print("1. 数据处理类示例")
        print("2. 算法实现示例")
        print("3. Web API设计示例")
        print("4. 设计模式示例")
        print("5. 运行所有示例")
        print("6. 自定义任务")
        print("0. 退出")
        print("="*50)
        
        choice = input("请选择选项 (0-6): ").strip()
        
        try:
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                await examples.example_1_data_processing()
            elif choice == "2":
                await examples.example_2_algorithm_implementation()
            elif choice == "3":
                await examples.example_3_web_api()
            elif choice == "4":
                await examples.example_4_design_pattern()
            elif choice == "5":
                await examples.run_all_examples()
            elif choice == "6":
                task = input("请输入您的编程任务描述: ").strip()
                if task:
                    await examples.custom_task(task)
                else:
                    print("❌ 任务描述不能为空")
            else:
                print("❌ 无效选项，请重新选择")
        
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 执行出错: {e}")


async def quick_demo():
    """快速演示"""
    print("🚀 AutoGen编程工作流快速演示")
    print("="*60)
    
    examples = WorkflowExamples()
    
    # 运行一个简单的示例
    simple_task = """
编写一个Python函数calculate_fibonacci(n)，要求：
1. 计算第n个斐波那契数
2. 使用动态规划优化性能
3. 处理边界情况（n <= 0）
4. 包含类型提示和文档字符串
5. 添加简单的测试用例
    """
    
    await examples.custom_task(simple_task)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        # 快速演示模式
        asyncio.run(quick_demo())
    else:
        # 交互模式
        asyncio.run(interactive_mode())
